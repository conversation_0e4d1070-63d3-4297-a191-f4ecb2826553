using Extras.TDengine.TDengIne;
using IotPlatform.Thing.Entity;
using SqlSugar.SplitTableExtensions;
using Microsoft.Extensions.Logging;

namespace IotPlatform.Application.Service.OpenApiServer;

/// <summary>
///     对接开放Api
/// </summary>
[ApiDescriptionSettings("开放Api")]
public class OpenApiService : IDynamicApiController, ITransient
{
    private readonly ISqlSugarRepository<OpenApiEntity> _openApi;
    private readonly ISqlSugarRepository<Model> _model;
    private readonly ISqlSugarRepository<ModelThing> _thing;
    private readonly ISqlSugarRepository<ModelAlarmRecord> _thingAlarmRecord;
    private readonly ReadService _readService;
    private readonly JsScriptEnginePool _enginePool;
    private readonly ILogger<OpenApiService> _logger;

    /// <summary>
    /// </summary>
    /// <param name="openApiEntity"></param>
    /// <param name="thingModel"></param>
    /// <param name="thingExample"></param>
    /// <param name="thingAlarmRecord"></param>
    /// <param name="readService"></param>
    /// <param name="enginePool"></param>
    /// <param name="logger"></param>
    public OpenApiService(
        ISqlSugarRepository<OpenApiEntity> openApiEntity,
        ISqlSugarRepository<Model> thingModel,
        ISqlSugarRepository<ModelThing> thingExample,
        ISqlSugarRepository<ModelAlarmRecord> thingAlarmRecord,
        ReadService readService,
        JsScriptEnginePool enginePool,
        ILogger<OpenApiService> logger)
    {
        _openApi = openApiEntity;
        _model = thingModel;
        _thing = thingExample;
        _thingAlarmRecord = thingAlarmRecord;
        _readService = readService;
        _enginePool = enginePool;
        _logger = logger;
    }

    #region Web管理

    /// <summary>
    ///     开放Api-列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/page")]
    public async Task<SqlSugarPagedList<OpenApiEntity>> OpenApiPage([FromQuery] OpenApiPageInput input)
    {
        SqlSugarPagedList<OpenApiEntity>? list = await _openApi.AsQueryable()
            .WhereIF(input.SearchValue.IsNotEmptyOrNull(),
                w => w.Name.Contains(input.SearchValue) || w.Token.Contains(input.SearchValue))
            .ToPagedListAsync(input.PageNo, input.PageSize);
        return list;
    }

    /// <summary>
    ///     开放Api-详情
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/detail")]
    [AllowAnonymous]
    public async Task<OpenApiEntity> OpenApiDetail([FromQuery] BaseId input)
    {
        OpenApiEntity? openApiEntity = await _openApi.AsQueryable().FirstAsync(f => f.Id == input.Id);
        if (openApiEntity == null)
        {
            throw Oops.Oh("数据已经被删除!");
        }

        return openApiEntity;
    }

    /// <summary>
    ///     开放Api--新增
    /// </summary>
    /// <returns></returns>
    [HttpPost("/open/add")]
    public async Task OpenApiEntityAdd(OpenApiEntityAddInput input)
    {
        bool isExist = await _openApi.IsAnyAsync(u => u.Name == input.Name);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        OpenApiEntity openApi = input.Adapt<OpenApiEntity>();
        await _openApi.InsertAsync(openApi);
    }

    /// <summary>
    ///     开放Api--修改
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/open/update")]
    public async Task OpenApiEntityUpdate(OpenApiEntityUpdateInput input)
    {
        bool isExist = await _openApi.IsAnyAsync(u => u.Name == input.Name && u.Id != input.Id);
        if (isExist)
        {
            throw Oops.Oh(ErrorCode.Com1004);
        }

        OpenApiEntity? openApi = await _openApi.GetFirstAsync(f => f.Id == input.Id);
        if (openApi == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        openApi = input.Adapt<OpenApiEntity>();
        await _openApi.AsSugarClient().Updateable(openApi).IgnoreColumns(w => w.CreatedTime).ExecuteCommandAsync();
    }

    /// <summary>
    ///     开放Api--删除
    /// </summary>
    /// <param name="input"></param>
    /// <returns></returns>
    [HttpPost("/open/delete")]
    public async Task OpenApiEntityDelete(BaseId input)
    {
        OpenApiEntity? openApi = await _openApi.AsQueryable().Where(f => f.Id == input.Id)
            .Includes(w => w.OpenApiDetailEntity)
            .FirstAsync();
        if (openApi == null)
        {
            throw Oops.Oh(ErrorCode.D1002);
        }

        await _openApi.AsSugarClient().DeleteNav(openApi).Include(w => w.OpenApiDetailEntity)
            .ExecuteCommandAsync();
    }

    /// <summary>
    ///     获取临时ToKen
    /// </summary>
    /// <param name="input"></param>
    /// <remarks>默认用户名/密码：admin/admin</remarks>
    /// <returns></returns>
    [HttpGet("/authorize/getToken")]
    [AllowAnonymous]
    public async Task<string> GetToken([FromQuery] GetTokenInput input)
    {
        if (input.Token.IsNotEmptyOrNull())
        {
            //Token认证
            OpenApiEntity? openApi = await _openApi.AsQueryable()
                .Where(f => f.AuthorizeType == AuthorizeTypeEnum.Token && f.Token == input.Token && f.Id == Convert.ToInt64(input.AppId))
                .Includes(w => w.OpenApiDetailEntity).FirstAsync();
            if (openApi == null)
            {
                throw Oops.Oh("身份认证失败！");
            }
        }
        else if (input.Sign.IsNotEmptyOrNull())
        {
            throw Oops.Oh("暂未实现!");
        }
        else
        {
            throw Oops.Oh("暂不支持!");
        }

        // 生成Token令牌
        string? accessToken = JWTEncryption.Encrypt(new Dictionary<string, object>
        {
            {ClaimConst.LoginMode, 2}
        }, 120);
        // 设置刷新Token令牌
        return accessToken;
    }

    #endregion

    #region Api文档

    /// <summary>
    ///     开放Api--授权Api列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/select")]
    public Task<Dictionary<string, List<string>>> OpenApiEntitySelect()
    {
        Dictionary<string, List<string>> resultData = new();
        Assembly assembly = Assembly.GetExecutingAssembly();
        Type[] types = assembly.GetTypes().Where(w => w.IsPublic && !w.IsSealed && !w.IsAbstract).ToArray();

        foreach (Type type in types)
        {
            MethodInfo[] methods = type.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly)
                .Where(w => !w.IsSpecialName).ToArray();
            foreach (MethodInfo method in methods)
            {
                OpenApiAttribute? attribute = method.GetCustomAttribute<OpenApiAttribute>();
                if (attribute == null)
                {
                    continue;
                }

                if (resultData.ContainsKey(attribute.Module))
                {
                    List<string> data = resultData[attribute.Module];
                    data.Add(attribute.Name);
                    resultData[attribute.Module] = data;
                }
                else
                {
                    resultData.Add(attribute.Module, new List<string> {attribute.Name});
                }
            }
        }

        return Task.FromResult(resultData);
    }

    /// <summary>
    ///     开放Api--已经授权Api列表
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/grant")]
    public async Task<List<string>> OpenApiEntityGrant([FromQuery] BaseId input)
    {
        List<string> output = new();
        OpenApiEntity? openApi = await _openApi.AsQueryable().Where(w => w.Id == input.Id).Includes(w => w.OpenApiDetailEntity)
            .FirstAsync();
        if (openApi == null)
        {
            throw Oops.Bah("配置已经被删除！");
        }

        foreach (OpenApiDetailEntity? openApiDetailEntity in openApi.OpenApiDetailEntity)
        {
            if (!output.Contains(openApiDetailEntity.Name))
            {
                output.Add(openApiDetailEntity.Name);
            }

            if (!output.Contains(openApiDetailEntity.Module))
            {
                output.Add(openApiDetailEntity.Module);
            }
        }

        return output;
    }

    /// <summary>
    ///     开放Api--授权Api
    /// </summary>
    /// <returns></returns>
    [HttpPost("/open/authorization")]
    public async Task OpenApiAuthorization([FromBody] OpenApiAuthorizationInput input)
    {
        OpenApiEntity? openApi = await _openApi.AsQueryable().Where(w => w.Id == input.Id).Includes(w => w.OpenApiDetailEntity)
            .FirstAsync();
        if (openApi == null)
        {
            throw Oops.Bah("配置已经被删除！");
        }

        openApi.OpenApiDetailEntity ??= new List<OpenApiDetailEntity>();
        openApi.OpenApiDetailEntity.Clear();
        Assembly assembly = Assembly.GetExecutingAssembly();
        Type[] types = assembly.GetTypes().Where(w => w.IsPublic && !w.IsSealed && !w.IsAbstract).ToArray();

        foreach (Type type in types)
        {
            MethodInfo[] methods = type.GetMethods(BindingFlags.Public | BindingFlags.Instance | BindingFlags.DeclaredOnly)
                .Where(w => !w.IsSpecialName).ToArray();
            foreach (MethodInfo method in methods)
            {
                try
                {
                    OpenApiAttribute? attribute = method.GetCustomAttribute<OpenApiAttribute>();
                    if (attribute == null)
                    {
                        continue;
                    }

                    if (!input.Names.Contains(attribute.Name))
                    {
                        continue;
                    }

                    OpenApiDetailEntity openApiDetailEntity = new()
                    {
                        HttpRequestType = attribute.HttpRequestType,
                        ModuleCore = attribute.ModuleCore, Module = attribute.Module, Name = attribute.Name,
                        Params = new List<RequestParams>(), OpenApiEntityId = input.Id, Result = new List<ResultParams>()
                    };

                    // 获取请求参数
                    ParameterInfo[] parameters = method.GetParameters();
                    object[] args = new object[parameters.Length];
                    for (int i = 0; i < parameters.Length; i++)
                    {
                        args[i] = Activator.CreateInstance(parameters[i].ParameterType);
                        PropertyInfo[] properties = parameters[i].ParameterType.GetProperties();

                        if (properties.Any())
                        {
                            foreach (PropertyInfo property in properties)
                            {
                                string? propertyType = property.PropertyType.HandleGenericType();

                                DescriptionAttribute? attribute2 = property.GetCustomAttribute(typeof(DescriptionAttribute)) as DescriptionAttribute;
                                bool isRequired = property.GetCustomAttributes(typeof(RequiredAttribute), false).Length > 0;
                                openApiDetailEntity.Params.Add(new RequestParams
                                {
                                    Name = property.Name,
                                    Type = propertyType,
                                    Remark = attribute2?.Description ?? "",
                                    IsRequired = isRequired
                                });
                            }
                        }
                        else
                        {
                            string? propertyType = parameters[i].ParameterType.HandleGenericType();
                            DescriptionAttribute? attribute2 = parameters[i].GetCustomAttribute(typeof(DescriptionAttribute)) as DescriptionAttribute;
                            bool isRequired = parameters[i].GetCustomAttributes(typeof(RequiredAttribute), false).Length > 0;
                            openApiDetailEntity.Params.Add(new RequestParams
                            {
                                Name = parameters[i].Name,
                                Type = propertyType,
                                Remark = attribute2?.Description ?? "",
                                IsRequired = isRequired
                            });
                        }
                    }

                    Type returnType = method.ReturnType;
                    //是否是异步
                    bool isAsync = method.GetCustomAttribute<AsyncMethodBuilderAttribute>() != null ||
                                   method.ToString().StartsWith(typeof(Task).FullName)
                                   || method.ToString().StartsWith(typeof(ValueTask).FullName);

                    Type finalType = returnType;
                    if (isAsync)
                    {
                        if (returnType == typeof(Task) || returnType == typeof(ValueTask))
                        {
                            finalType = typeof(void);
                        }
                        else if (returnType.IsGenericType)
                        {
                            finalType = returnType.GenericTypeArguments[0];
                        }
                    }
                    else
                    {
                        finalType = returnType;
                    }

                    if (typeof(IEnumerable).IsAssignableFrom(finalType) && finalType != typeof(string))
                    {
                        PropertyInfo[] propertyInfoPs = finalType.GenericTypeArguments[0].GetProperties();
                        foreach (PropertyInfo property in propertyInfoPs)
                        {
                            DescriptionAttribute? attribute2 =
                                property.GetCustomAttribute(typeof(DescriptionAttribute)) as DescriptionAttribute;
                            string? propertyType = property.PropertyType.HandleGenericType();
                            openApiDetailEntity.Result.Add(new ResultParams
                            {
                                Name = property.Name,
                                Type = propertyType,
                                Remark = attribute2?.Description ?? ""
                            });
                        }
                    }
                    else
                    {
                        PropertyInfo[] propertyInfoPs = finalType.GetProperties();
                        foreach (PropertyInfo property in propertyInfoPs)
                        {
                            DescriptionAttribute? attribute2 = property.GetCustomAttribute(typeof(DescriptionAttribute)) as DescriptionAttribute;
                            string? propertyType = property.PropertyType.HandleGenericType();
                            openApiDetailEntity.Result.Add(new ResultParams
                            {
                                Name = property.Name,
                                Type = propertyType,
                                Remark = attribute2?.Description ?? ""
                            });
                        }
                    }

                    // 获取 API 的 URL
                    HttpGetAttribute? route = method.GetCustomAttribute<HttpGetAttribute>();
                    if (route != null)
                    {
                        openApiDetailEntity.Path = route.Template ?? "";
                    }
                    else
                    {
                        HttpPostAttribute? route2 = method.GetCustomAttribute<HttpPostAttribute>();
                        if (route2 != null)
                        {
                            openApiDetailEntity.Path = route2.Template ?? "";
                        }
                    }

                    openApi.OpenApiDetailEntity.Add(openApiDetailEntity);
                }
                catch (Exception e)
                {
                    Log.Error("开放Api解析:" + e.Message);
                }
            }
        }

        await _openApi.AsSugarClient().UpdateNav(openApi).Include(w => w.OpenApiDetailEntity).ExecuteCommandAsync();
    }

    /// <summary>
    ///     开放Api--获取授权模块
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/{id}/getModuleList")]
    [AllowAnonymous]
    public async Task<List<GetModuleListOutput>> GetModuleList(long id)
    {
        OpenApiEntity? openApi = await _openApi.AsQueryable().Where(w => w.Id == id).Includes(w => w.OpenApiDetailEntity).FirstAsync();
        if (openApi == null)
        {
            throw Oops.Bah("配置已经被删除！");
        }

        List<GetModuleListOutput> output = new();
        foreach (OpenApiDetailEntity? openApiDetail in from openApiDetail in openApi.OpenApiDetailEntity
                 let cores = output.Select(s => s.Core)
                 where !cores.Contains(openApiDetail.ModuleCore)
                 select openApiDetail)
        {
            output.Add(new GetModuleListOutput {Core = openApiDetail.ModuleCore, Module = openApiDetail.Module});
        }

        return output;
    }

    /// <summary>
    ///     开放Api--获取api描述信息
    /// </summary>
    /// <returns></returns>
    [HttpGet("/open/{id}/getApiDocs")]
    [AllowAnonymous]
    public async Task<GetApiDocsOutput> GetApiDocs(long id, string core)
    {
        OpenApiEntity? openApi = await _openApi.AsQueryable().Where(w => w.Id == id).Includes(w => w.OpenApiDetailEntity).FirstAsync();
        if (openApi == null)
        {
            throw Oops.Bah("配置已经被删除！");
        }

        GetApiDocsOutput output = openApi.Adapt<GetApiDocsOutput>();
        output.Result ??= new List<ResultOutput>();
        output.Result = openApi.OpenApiDetailEntity.Where(w => w.ModuleCore == core).Adapt<List<ResultOutput>>();
        foreach (ResultOutput openApiDetailEntity in output.Result)
        {
            openApiDetailEntity.Params = openApiDetailEntity.Params.OrderByDescending(o => o.IsRequired).ThenBy(o => o.Name).ToList();
        }

        return output;
    }

    #endregion

    #region 开放Api

    #region 接入与建模

    /// <summary>
    ///     开放Api --查询物模型
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/things-model/things-classes")]
    [OpenApi(Name = "查询物模型", Module = "接入与建模", ModuleCore = "AccessAndModeling")]
    [AllowAnonymous]
    public async Task<List<QueryModelOutput>> QueryModel([FromQuery] OpenApiThingClassesInput input, [Required] [Description("应用Id")] long AppId)
    {
        List<Model> thingPageOutput = await _model.AsQueryable()
            .Where(w => w.IsTemplate == false)
            .Where(w => w.ModelType == input.ModelType)
            .Where(w => w.Enable == true)
            .WhereIF(input.ModelId.IsNotEmptyOrNull(), w => w.Uuid == input.ModelId)
            .WhereIF(input.Name.IsNotEmptyOrNull(), w => w.Name.Contains(input.Name))
            .WhereIF(input.Tag.IsNotEmptyOrNull(), w => SqlFunc.JsonLike(w.Tags, input.Tag))
            .ToListAsync();
        return thingPageOutput.Adapt<List<QueryModelOutput>>();
    }

    /// <summary>
    ///     开放Api --查询物实例
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/model-instance/instances")]
    [OpenApi(Name = "查询物实例", Module = "接入与建模", ModuleCore = "AccessAndModeling")]
    [AllowAnonymous]
    public async Task<List<QueryInstanceOutput>> QueryInstance([FromQuery] OpenApiThingInstancesInput input, [Required] [Description("应用Id")] long AppId)
    {
        List<ModelThing> thingPageOutput = await _thing.AsQueryable()
            .Where(w => w.ModelType == input.ModelType)
            .WhereIF(!string.IsNullOrEmpty(input.ThingId), w => w.Identification == input.ThingId)
            .WhereIF(input.Name.IsNotEmptyOrNull(), w => w.Name.Contains(input.Name))
            .WhereIF(input.Tag.IsNotEmptyOrNull(), w => SqlFunc.JsonLike(w.Tags, input.Tag))
            .Includes(w => w.Model)
            .WhereIF(!string.IsNullOrEmpty(input.ModelId), w => w.Model.Uuid == input.ModelId)
            .Includes(w => w.ModelGroup)
            .WhereIF(!string.IsNullOrEmpty(input.ModelGroupName), w => w.ModelGroup.Name == input.ModelGroupName)
            .ToListAsync();
        List<QueryInstanceOutput> output = new();
        foreach (ModelThing thingPage in thingPageOutput)
        {
            QueryInstanceOutput queryInstanceOutput = thingPage.Adapt<QueryInstanceOutput>();
            queryInstanceOutput.ModelId = thingPage.Model.Uuid;
            queryInstanceOutput.ThingGroupName = thingPage.ModelGroup?.Name ?? "";
            output.Add(queryInstanceOutput);
        }

        return output;
    }

    #endregion

    #region 报警管理

    /// <summary>
    ///     开放Api --当前报警列表查询
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/alarm-event/current/alarms")]
    [OpenApi(Name = "当前报警列表查询", Module = "报警管理", ModuleCore = "AlarmManagement")]
    [AllowAnonymous]
    public async Task<List<QueryCurrentAlarmListOutput>> QueryCurrentAlarmList([FromQuery] QueryCurrentAlarmListInput input, [Required] [Description("应用Id")] long AppId)
    {
        SqlSugarPagedList<ModelAlarmRecord>? thingPage = await _thingAlarmRecord.AsQueryable()
            .WhereIF(input.ModelIds.Any(), w => input.ModelIds.Contains(w.ModelId))
            .WhereIF(input.ThingIds.Any(), w => input.ThingIds.Contains(w.ThingId))
            .WhereIF(input.ModelIdens.Any(), w => input.ModelIdens.Contains(w.ModelUuId))
            .WhereIF(input.ThingIdens.Any(), w => input.ThingIdens.Contains(w.ThingIdent))
            .WhereIF(input.Type == 1, w => w.Status == ThingAlarmRecordStatusEnum.Alarm)
            .WhereIF(input.AlarmIdens.Any(), w => input.AlarmIdens.Contains(w.ModelAlarmIdent))
            .WhereIF(input.AlarmIds.Any(), w => input.AlarmIds.Contains(w.ModelAlarmId))
            .SplitTable()
            .Includes(w => w.Model)
            .Includes(w => w.ModelAlarm)
            .WhereIF(input.SeverityLevels.Any(), w => input.SeverityLevels.Contains((int) w.ModelAlarm.AlarmLevel))
            .WhereIF(input.Subject.IsNotEmptyOrNull(), u => u.ModelAlarm.Name.Contains(input.Subject))
            .WhereIF(input.Tag.IsNotEmptyOrNull(), w => SqlFunc.JsonLike(w.ModelAlarm.Tags, input.Tag))
            .OrderBy(w => w.Status)
            .ToPagedListAsync(1, input.Limit);
        return thingPage.Rows.Adapt<List<QueryCurrentAlarmListOutput>>();
    }

    /// <summary>
    ///     开放Api --多条件报警列表查询
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/alarm-event/historian/alarms/all")]
    [OpenApi(Name = "多条件报警列表查询", Module = "报警管理", ModuleCore = "AlarmManagement")]
    [AllowAnonymous]
    public async Task<List<QueryCurrentAlarmListOutput>> QueryAlarmListWithMultipleConditions([FromQuery] QueryCurrentAlarmListInput input, [Required] [Description("应用Id")] long AppId)
    {
        SqlSugarPagedList<ModelAlarmRecord>? thingPage = await _thingAlarmRecord.AsQueryable()
            .WhereIF(input.ModelIds.Any(), w => input.ModelIds.Contains(w.ModelId))
            .WhereIF(input.ThingIds.Any(), w => input.ThingIds.Contains(w.ThingId))
            .WhereIF(input.ModelIdens.Any(), w => input.ModelIdens.Contains(w.ModelUuId))
            .WhereIF(input.ThingIdens.Any(), w => input.ThingIdens.Contains(w.ThingIdent))
            .WhereIF(input.Type == 1, w => w.CloseTime == null || w.ConfirmTime == null)
            .WhereIF(input.AlarmIdens.Any(), w => input.AlarmIdens.Contains(w.ModelAlarmIdent))
            .WhereIF(input.AlarmIds.Any(), w => input.AlarmIds.Contains(w.ModelAlarmId))
            .SplitTable()
            .Includes(w => w.Model)
            .Includes(w => w.ModelAlarm)
            .WhereIF(input.SeverityLevels.Any(), w => input.SeverityLevels.Contains((int) w.ModelAlarm.AlarmLevel))
            .WhereIF(input.Subject.IsNotEmptyOrNull(), u => u.ModelAlarm.Name.Contains(input.Subject))
            .WhereIF(input.Tag.IsNotEmptyOrNull(), w => SqlFunc.JsonLike(w.ModelAlarm.Tags, input.Tag))
            .OrderBy(w => w.Status)
            .ToPagedListAsync(1, input.Limit);
        return thingPage.Rows.Adapt<List<QueryCurrentAlarmListOutput>>();
    }

    #endregion

    #region 实时数据查询

    /// <summary>
    ///     开放Api --查询同一模型的一组物实例的实时数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/realtime/models/things")]
    [OpenApi(Name = "查询同一模型的物实例的实时数据", Module = "实时数据查询", ModuleCore = "QueryRealtimeData")]
    [AllowAnonymous]
    public async Task<dynamic> GetRealtimeDataOfModelInstances([FromQuery] GetRealtimeDataOfModelInstancesInput input, [Required] [Description("应用Id")] long AppId)
    {
        // 默认返回全部实例
        if (input.ThingIds.Count == 0)
        {
            Model model = await _model.AsQueryable().Where(w => w.Uuid == input.ModelId).Includes(w => w.ThingExample).FirstAsync();
            if (model == null)
            {
                throw Oops.Oh($"未找到：{input.ModelId}模型标识");
            }

            input.ThingIds = model.ThingExample.Select(s => s.Identification).ToList();
        }

        return _readService.GetRealtimeDataOfModelInstances(input);
    }

    /// <summary>
    ///     开放Api --查询指定物实例的实时数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/realtime/models/model")]
    [OpenApi(Name = "查询指定物实例的实时数据", Module = "实时数据查询", ModuleCore = "QueryRealtimeData")]
    [AllowAnonymous]
    public dynamic GetRealtimeDataOfInstance([FromQuery] GetRealtimeDataOfInstanceInput input, [Required] [Description("应用Id")] long AppId)
    {
        return _readService.GetRealtimeDataOfInstance(input);
    }

    #endregion

    #region 历史数据查询

    /// <summary>
    ///     开放Api --查询同一模型的一组物实例的历史数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/historian/models/things")]
    [OpenApi(Name = "查询同一模型的物实例的历史数据", Module = "历史数据查询", ModuleCore = "QueryHistoricalData")]
    [AllowAnonymous]
    public async Task<dynamic> GetModelInstancesHistoricalData([FromQuery] GetModelInstancesHistoricalDataInput input, [Required] [Description("应用Id")] long AppId)
    {
        // 默认返回全部实例
        if (input.ThingIds.Count == 0)
        {
            Model model = await _model.AsQueryable().Where(w => w.Uuid == input.ModelId).Includes(w => w.ThingExample)
                .FirstAsync();
            if (model == null)
            {
                throw Oops.Oh($"未找到：{input.ModelId}模型标识");
            }

            input.ThingIds = model.ThingExample.Select(s => s.Identification).ToList();
        }
        return _readService.GetModelInstancesHistoricalData(input);
    }

    /// <summary>
    ///     开放Api --查询同一模型的一组物实例的时间窗口数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/historian/models/things/window")]
    [OpenApi(Name = "查询同一模型的一组物实例的时间窗口数据", Module = "历史数据查询", ModuleCore = "QueryHistoricalData")]
    [AllowAnonymous]
    public async Task<dynamic> GetTimeWindowDataOfModelInstances([FromQuery] GetTimeWindowDataOfModelInstancesInput input, [Required] [Description("应用Id")] long AppId)
    {
        // 默认返回全部实例
        if (input.ThingIds.Count == 0)
        {
            Model model = await _model.AsQueryable().Where(w => w.Uuid == input.ModelId).Includes(w => w.ThingExample)
                .FirstAsync();
            if (model == null)
            {
                throw Oops.Oh($"未找到：{input.ModelId}模型标识");
            }

            input.ThingIds = model.ThingExample.Select(s => s.Identification).ToList();
        }
        return _readService.GetTimeWindowDataOfModelInstances(input);
    }

    /// <summary>
    ///     开放Api --查询指定物实例的时间聚集数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/historian/models/things/aggregation")]
    [OpenApi(Name = "查询指定物实例的时间聚集数据", Module = "历史数据查询", ModuleCore = "QueryHistoricalData")]
    [AllowAnonymous]
    public async Task<dynamic> GetAggregatedDataOfInstance([FromQuery] GetAggregatedDataOfInstanceInput input, [Required] [Description("应用Id")] long AppId)
    {
        // 默认返回全部实例
        if (input.ThingIds.Count == 0)
        {
            Model model = await _model.AsQueryable().Where(w => w.Uuid == input.ModelId).Includes(w => w.ThingExample)
                .FirstAsync();
            if (model == null)
            {
                throw Oops.Oh($"未找到：{input.ModelId}模型标识");
            }

            input.ThingIds = model.ThingExample.Select(s => s.Identification).ToList();
        }
        return _readService.GetAggregatedDataOfInstance(input);
    }

    /// <summary>
    ///     开放Api --查询指定物实例的历史数据
    /// </summary>
    /// <returns></returns>
    [HttpGet("/openapi/historian/models/model")]
    [OpenApi(Name = "查询指定物实例的历史数据", Module = "历史数据查询", ModuleCore = "QueryHistoricalData")]
    [AllowAnonymous]
    public dynamic GetHistoricalDataOfInstance([FromQuery] GetHistoricalDataOfInstanceInput input, [Required] [Description("应用Id")] long AppId)
    {
        return _readService.GetHistoricalDataOfInstance(input);
    }

    #endregion

    #region 定制Api

    
    /// <summary>
    ///     开放Api --运行脚本
    /// </summary>
    /// <returns></returns>
    [HttpPost("/openapi/script/run")]
    [OpenApi(Name = "运行脚本", Module = "定制Api", ModuleCore = "Personality", HttpRequestType = HttpRequestTypeEnum.Post)]
    [AllowAnonymous]
    public async Task<dynamic> RunScript([FromBody] RunScriptInput input)
    {
        try
        {
            // 根据Id获取脚本配置
            var script = await _model.AsSugarClient()
                .Queryable<ProgramBlock.Entity.ProgramBlock>()
                .FirstAsync(f => f.Id == input.Id);

            if (script == null)
            {
                throw Oops.Oh("脚本不存在，请确认后重试！");
            }

            if (string.IsNullOrEmpty(script.Content))
            {
                throw Oops.Oh("脚本内容为空！");
            }

            // 使用脚本引擎池执行脚本
            await using var context = await _enginePool.CreateContextAsync();

            // 设置变量
            foreach (var (key, value) in input.Values)
            {
                try
                {
                    if (value != null)
                    {
                        context.SetVariable(key, value);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "设置脚本变量失败: {Key}={Value}", key, value);
                }
            }

            // 执行脚本
            var (result, scriptLog) = await context.ExecuteAsync(
                $"openapi-script-{script.Id}",
                script.Content,
                new { timestamp = DateTime.Now }
            );
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行脚本失败 - ID: {ScriptId}", input.Id);
            throw;
        }
    }

    /// <summary>
    ///     开放Api --运行脚本(纯JSON格式)
    /// </summary>
    /// <returns></returns>
    [HttpPost("/openapi/{appId}/script/{id}/run")]
    [OpenApi(Name = "运行脚本(纯JSON格式)", Module = "定制Api", ModuleCore = "Personality", HttpRequestType = HttpRequestTypeEnum.Post)]
    [AllowAnonymous]
    public async Task<dynamic> RunScriptWithPureJson(long appId, long id, [FromBody] Dictionary<string, object> parameters)
    {
        try
        {
            // 根据Id获取脚本配置
            var script = await _model.AsSugarClient()
                .Queryable<ProgramBlock.Entity.ProgramBlock>()
                .FirstAsync(f => f.Id == id);

            if (script == null)
            {
                throw Oops.Oh("脚本不存在，请确认后重试！");
            }

            if (string.IsNullOrEmpty(script.Content))
            {
                throw Oops.Oh("脚本内容为空！");
            }

            // 使用脚本引擎池执行脚本
            await using var context = await _enginePool.CreateContextAsync();

            // 设置变量
            if (parameters != null)
            {
                foreach (var (key, value) in parameters)
                {
                    try
                    {
                        if (value != null)
                        {
                            context.SetVariable(key, value);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "设置脚本变量失败: {Key}={Value}", key, value);
                    }
                }
            }

            // 执行脚本
            var (result, scriptLog) = await context.ExecuteAsync(
                $"openapi-script-pure-{script.Id}",
                script.Content,
                new { timestamp = DateTime.Now, appId = appId }
            );

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "执行脚本失败 - AppId: {AppId}, ScriptId: {ScriptId}", appId, id);
            throw;
        }
    }

    #endregion

    #endregion
}