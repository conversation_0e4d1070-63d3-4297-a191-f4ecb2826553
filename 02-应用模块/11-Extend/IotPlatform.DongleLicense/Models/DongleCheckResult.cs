using IotPlatform.DongleLicense.Services;

namespace IotPlatform.DongleLicense.Models;

/// <summary>
/// 加密锁检查结果
/// </summary>
public class DongleCheckResult
{
    /// <summary>
    /// 检查是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 检查时间
    /// </summary>
    public DateTime CheckTime { get; set; }

    /// <summary>
    /// 加密锁数量
    /// </summary>
    public int DongleCount { get; set; }

    /// <summary>
    /// 加密锁设备信息（系统中始终只有一个设备）
    /// </summary>
    public DongleDeviceInfo? DongleInfo { get; set; }

    /// <summary>
    /// 操作耗时（毫秒）
    /// </summary>
    public long ElapsedMilliseconds { get; set; }

    /// <summary>
    /// 是否通过授权验证
    /// </summary>
    public bool IsAuthorized { get; set; }

    /// <summary>
    /// 授权验证状态描述
    /// </summary>
    public string AuthorizationStatus { get; set; } = "未验证";
}

/// <summary>
/// 加密锁设备信息
/// </summary>
public class DongleDeviceInfo
{
    /// <summary>
    /// 设备索引
    /// </summary>
    public int Index { get; set; }

    /// <summary>
    /// COS版本
    /// </summary>
    public string Version { get; set; } = string.Empty;

    /// <summary>
    /// 产品类型描述
    /// </summary>
    public string ProductType { get; set; } = string.Empty;

    /// <summary>
    /// 出厂日期
    /// </summary>
    public string BirthDay { get; set; } = string.Empty;

    /// <summary>
    /// 代理商编号
    /// </summary>
    public string AgentId { get; set; } = string.Empty;

    /// <summary>
    /// 产品ID
    /// </summary>
    public string ProductId { get; set; } = string.Empty;

    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// 硬件ID
    /// </summary>
    public string HardwareId { get; set; } = string.Empty;

    /// <summary>
    /// 是否为母锁
    /// </summary>
    public bool IsMother { get; set; }

    /// <summary>
    /// 设备类型
    /// </summary>
    public string DeviceType { get; set; } = string.Empty;

    /// <summary>
    /// 是否可以正常打开和关闭
    /// </summary>
    public bool CanOpenClose { get; set; }

    /// <summary>
    /// 打开关闭测试的错误信息
    /// </summary>
    public string? OpenCloseError { get; set; }

    /// <summary>
    /// 从DONGLE_INFO结构创建设备信息
    /// </summary>
    /// <param name="dongleInfo">加密锁信息结构</param>
    /// <param name="index">设备索引</param>
    /// <returns>设备信息对象</returns>
    public static DongleDeviceInfo FromDongleInfo(DongleApiService.DONGLE_INFO dongleInfo, int index)
    {
        var deviceInfo = new DongleDeviceInfo
        {
            Index = index,
            Version = $"{dongleInfo.m_Ver:X4}",
            AgentId = dongleInfo.m_Agent.ToString("X"),
            ProductId = dongleInfo.m_PID.ToString("X"),
            UserId = dongleInfo.m_UserID.ToString("X"),
            IsMother = dongleInfo.m_IsMother == 1,
            DeviceType = dongleInfo.m_DevType.ToString()
        };

        // 解析产品类型
        deviceInfo.ProductType = dongleInfo.m_Type switch
        {
            0xFF => "标准版",
            0x00 => "时钟锁",
            0x01 => "带时钟的U盘锁",
            0x02 => "标准U盘锁",
            _ => $"未知类型({dongleInfo.m_Type:X})"
        };

        // 解析出厂日期
        if (dongleInfo.m_BirthDay != null && dongleInfo.m_BirthDay.Length >= 6)
        {
            try
            {
                deviceInfo.BirthDay = $"20{dongleInfo.m_BirthDay[0]:X2}-{dongleInfo.m_BirthDay[1]:X2}-{dongleInfo.m_BirthDay[2]:X2} " +
                                    $"{dongleInfo.m_BirthDay[3]:X2}:{dongleInfo.m_BirthDay[4]:X2}:{dongleInfo.m_BirthDay[5]:X2}";
            }
            catch
            {
                deviceInfo.BirthDay = "解析失败";
            }
        }

        // 解析硬件ID
        if (dongleInfo.m_HID != null && dongleInfo.m_HID.Length >= 8)
        {
            try
            {
                deviceInfo.HardwareId = string.Join(" ", dongleInfo.m_HID.Take(8).Select(b => b.ToString("X2")));
            }
            catch
            {
                deviceInfo.HardwareId = "解析失败";
            }
        }

        return deviceInfo;
    }
}

/// <summary>
/// 加密锁检查配置
/// </summary>
public class DongleCheckConfig
{
    /// <summary>
    /// 检查间隔（分钟）
    /// </summary>
    public int CheckIntervalMinutes { get; set; } = 5;

    /// <summary>
    /// 是否启用检查
    /// </summary>
    public bool EnableCheck { get; set; } = true;

    /// <summary>
    /// 打开关闭操作超时时间（毫秒）
    /// </summary>
    public int OpenCloseTimeoutMs { get; set; } = 5000;

    /// <summary>
    /// 是否记录详细日志
    /// </summary>
    public bool EnableDetailedLogging { get; set; } = true;

    /// <summary>
    /// 检查失败时是否发送告警
    /// </summary>
    public bool SendAlarmOnFailure { get; set; } = true;

    /// <summary>
    /// 健康检查缓存时间（秒）- 用于GetHealth接口性能优化
    /// </summary>
    public int HealthCheckCacheSeconds { get; set; } = 30;

    /// <summary>
    /// 是否启用快速健康检查模式（跳过硬件操作测试）
    /// </summary>
    public bool EnableFastHealthCheck { get; set; } = true;

    /// <summary>
    /// 快速检查时的最大缓存时间（秒）
    /// </summary>
    public int FastCheckMaxCacheSeconds { get; set; } = 60;

    /// <summary>
    /// 健康检查失败时的重试间隔（秒）
    /// </summary>
    public int HealthCheckRetryIntervalSeconds { get; set; } = 5;
}
