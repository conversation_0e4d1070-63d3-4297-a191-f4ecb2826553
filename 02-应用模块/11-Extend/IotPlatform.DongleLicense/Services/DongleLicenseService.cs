using System.Security.Cryptography;
using System.Text;
using Furion.DependencyInjection;
using Furion.DynamicApiController;
using IotPlatform.DongleLicense.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
///     加密锁许可证管理服务
/// </summary>
[ApiDescriptionSettings("加密锁许可证")]
public class DongleLicenseService : IDynamicApiController, ITransient
{
    private readonly DongleTriggerService _triggerService;
    private readonly DongleAuthService _authService;
    private readonly IServiceProvider _serviceProvider;
    private readonly DongleCheckConfig _config;

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="triggerService">触发服务</param>
    /// <param name="authService">授权服务</param>
    /// <param name="serviceProvider">服务提供者</param>
    /// <param name="config">加密锁检查配置</param>
    public DongleLicenseService(
        DongleTriggerService triggerService,
        DongleAuthService authService,
        IServiceProvider serviceProvider,
        IOptions<DongleCheckConfig> config)
    {
        _triggerService = triggerService;
        _authService = authService;
        _serviceProvider = serviceProvider;
        _config = config.Value;
    }

    /// <summary>
    ///     手动触发检查
    /// </summary>
    /// <returns>检查结果</returns>
    [HttpPost("/dongleLicense/triggerCheck")]
    public async Task<DongleCheckResult> TriggerCheck()
    {
        return await _triggerService.TriggerCheckAsync();
    }

    /// <summary>
    ///     获取系统健康状态（包含加密锁状态）- 性能优化版本
    /// </summary>
    /// <returns>健康状态：true表示授权验证通过，false表示验证失败</returns>
    [HttpGet("/dongleLicense/health")]
    public async Task<bool> GetHealth()
    {
        // 如果配置中禁用了检查，直接返回 true，避免前端无法正常使用
        if (!_config.EnableCheck)
        {
            return true;
        }

        // 使用快速健康检查服务，优先从缓存获取结果
        using var scope = _serviceProvider.CreateScope();
        var fastHealthService = scope.ServiceProvider.GetRequiredService<DongleFastHealthService>();

        return await fastHealthService.GetHealthAsync();
    }

    /// <summary>
    ///     初始化授权密钥（一次性操作）
    /// </summary>
    /// <param name="input">初始化授权输入参数</param>
    /// <returns>初始化结果</returns>
    [HttpPost("/dongleLicense/initializeAuth")]
    public async Task<dynamic> InitializeAuthorization([FromBody] InitializeAuthInput input)
    {
        try
        {
            // 参数验证
            if (input == null || string.IsNullOrWhiteSpace(input.HardwareId))
            {
                return new
                {
                    success = false,
                    message = "硬件ID不能为空",
                    timestamp = DateTime.Now
                };
            }

            // 检查是否已经初始化过
            if (_authService.IsAuthInitialized())
            {
                return new
                {
                    success = false,
                    message = "授权密钥已存在，无法重复初始化",
                    timestamp = DateTime.Now
                };
            }

            // 保存用户传入的授权密钥到文件
            bool saveResult = _authService.SaveAuthKey(input.HardwareId.Trim());
            if (!saveResult)
            {
                return new
                {
                    success = false,
                    message = "保存授权密钥失败",
                    timestamp = DateTime.Now
                };
            }

            return new
            {
                success = true,
                message = "授权密钥初始化成功",
                hardwareId = input.HardwareId.Trim(),
                timestamp = DateTime.Now
            };
        }
        catch (Exception ex)
        {
            return new
            {
                success = false,
                message = $"初始化授权密钥时发生异常: {ex.Message}",
                timestamp = DateTime.Now
            };
        }
    }

    /// <summary>
    ///     检查是否已初始化授权密钥
    /// </summary>
    /// <returns>授权初始化状态</returns>
    [HttpGet("/dongleLicense/hasAuthKey")]
    public dynamic HasAuthorizationKey()
    {
        bool isInitialized = _authService.IsAuthInitialized();

        return new
        {
            hasAuthKey = isInitialized,
            isInitialized,
            message = isInitialized ? "已初始化授权密钥" : "未初始化授权密钥",
            timestamp = DateTime.Now
        };
    }

    /// <summary>
    ///     清空授权密钥文件（仅用于测试阶段）
    /// </summary>
    /// <returns>清空结果</returns>
    [HttpDelete("/dongleLicense/clearAuthKey")]
    public dynamic ClearAuthorizationKey()
    {
        try
        {
            bool clearResult = _authService.ClearAuthKey();

            if (clearResult)
            {
                return new
                {
                    success = true,
                    message = "授权密钥文件已清空",
                    timestamp = DateTime.Now
                };
            }
            else
            {
                return new
                {
                    success = false,
                    message = "清空授权密钥文件失败",
                    timestamp = DateTime.Now
                };
            }
        }
        catch (Exception ex)
        {
            return new
            {
                success = false,
                message = $"清空授权密钥文件时发生异常: {ex.Message}",
                timestamp = DateTime.Now
            };
        }
    }
}