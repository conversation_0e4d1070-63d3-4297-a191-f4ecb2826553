using IotPlatform.DongleLicense.Models;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Diagnostics;

namespace IotPlatform.DongleLicense.Services;

/// <summary>
/// 加密锁快速健康检查服务 - 专门用于优化GetHealth接口性能
/// </summary>
public class DongleFastHealthService
{
    private readonly DongleLicenseManager _licenseManager;
    private readonly DongleCheckService _dongleCheckService;
    private readonly DongleAuthService _authService;
    private readonly ILogger<DongleFastHealthService> _logger;
    private readonly DongleCheckConfig _config;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="licenseManager">许可证管理器</param>
    /// <param name="dongleCheckService">加密锁检查服务</param>
    /// <param name="authService">授权服务</param>
    /// <param name="logger">日志记录器</param>
    /// <param name="config">配置选项</param>
    public DongleFastHealthService(
        DongleLicenseManager licenseManager,
        DongleCheckService dongleCheckService,
        DongleAuthService authService,
        ILogger<DongleFastHealthService> logger,
        IOptions<DongleCheckConfig> config)
    {
        _licenseManager = licenseManager;
        _dongleCheckService = dongleCheckService;
        _authService = authService;
        _logger = logger;
        _config = config.Value;
    }

    /// <summary>
    /// 快速健康检查 - 优先使用缓存，必要时执行轻量级检查
    /// </summary>
    /// <returns>健康状态</returns>
    public async Task<bool> GetHealthAsync()
    {
        var stopwatch = Stopwatch.StartNew();
        bool fromCache = false;
        bool healthResult = false;
        string checkType = "Unknown";

        try
        {
            // 1. 首先尝试从缓存获取结果
            if (_config.EnableFastHealthCheck)
            {
                var cachedResult = _licenseManager.GetCachedHealthCheckResult();
                if (cachedResult.HasValue)
                {
                    stopwatch.Stop();
                    fromCache = true;
                    healthResult = cachedResult.Value;
                    checkType = "CachedFast";

                    if (_config.EnableDetailedLogging)
                    {
                        _logger.LogDebug("快速健康检查使用缓存结果: {Result}, 耗时: {ElapsedMs}ms",
                            healthResult, stopwatch.ElapsedMilliseconds);
                    }

                    return healthResult;
                }
            }

            // 2. 检查是否需要执行健康检查
            if (!_licenseManager.ShouldPerformHealthCheck())
            {
                // 返回最后一次的检查结果
                healthResult = _licenseManager.LastHealthCheckResult ?? false;
                stopwatch.Stop();

                if (_config.EnableDetailedLogging)
                {
                    _logger.LogDebug("跳过健康检查，使用最后结果: {Result}, 耗时: {ElapsedMs}ms",
                        healthResult, stopwatch.ElapsedMilliseconds);
                }

                return healthResult;
            }

            // 3. 执行健康检查
            if (_config.EnableFastHealthCheck)
            {
                healthResult = await PerformFastHealthCheckAsync();
            }
            else
            {
                healthResult = await PerformFullHealthCheckAsync();
            }

            // 4. 更新缓存和状态
            _licenseManager.UpdateHealthCheckResult(healthResult);

            stopwatch.Stop();
            if (_config.EnableDetailedLogging)
            {
                _logger.LogInformation("健康检查完成: {Result}, 模式: {Mode}, 耗时: {ElapsedMs}ms",
                    healthResult,
                    _config.EnableFastHealthCheck ? "快速" : "完整",
                    stopwatch.ElapsedMilliseconds);
            }

            return healthResult;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "健康检查发生异常，耗时: {ElapsedMs}ms", stopwatch.ElapsedMilliseconds);

            // 异常情况下更新状态
            _licenseManager.UpdateHealthCheckResult(false);

            return false;
        }
    }

    /// <summary>
    /// 执行快速健康检查（仅检查授权，不进行硬件操作）
    /// </summary>
    /// <returns>健康状态</returns>
    private async Task<bool> PerformFastHealthCheckAsync()
    {
        try
        {
            // 1. 检查是否有存储的授权密钥
            string? storedAuthKey = _authService.GetStoredAuthKey();
            if (string.IsNullOrEmpty(storedAuthKey))
            {
                if (_config.EnableDetailedLogging)
                {
                    _logger.LogDebug("快速健康检查失败：未找到授权密钥文件");
                }
                return false;
            }

            // 2. 尝试使用最后一次检查的设备信息
            var lastCheckResult = _licenseManager.LastCheckResult;
            if (lastCheckResult?.DongleInfo != null && !string.IsNullOrEmpty(lastCheckResult.DongleInfo.HardwareId))
            {
                // 使用缓存的硬件ID进行授权验证
                var (isValid, status) = _authService.ValidateAuth(lastCheckResult.DongleInfo.HardwareId);
                
                if (_config.EnableDetailedLogging)
                {
                    _logger.LogDebug("快速健康检查使用缓存硬件ID: {HardwareId}, 验证结果: {IsValid}", 
                        lastCheckResult.DongleInfo.HardwareId, isValid);
                }
                
                return isValid;
            }

            // 3. 如果没有缓存的设备信息，执行轻量级设备检查
            return await PerformLightweightDeviceCheckAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "快速健康检查过程中发生异常");
            return false;
        }
    }

    /// <summary>
    /// 执行轻量级设备检查（仅枚举设备，不进行打开/关闭测试）
    /// </summary>
    /// <returns>健康状态</returns>
    private async Task<bool> PerformLightweightDeviceCheckAsync()
    {
        try
        {
            if (_config.EnableDetailedLogging)
            {
                _logger.LogDebug("执行轻量级设备检查...");
            }

            // 创建一个轻量级的检查结果
            var result = new DongleCheckResult
            {
                CheckTime = DateTime.Now
            };

            // 使用现有的检查服务，但跳过硬件操作测试
            var fullResult = await _dongleCheckService.CheckDongleAsync();
            
            // 只关心基本的设备检测和授权验证
            bool isHealthy = fullResult.IsSuccess && fullResult.IsAuthorized;
            
            if (_config.EnableDetailedLogging)
            {
                _logger.LogDebug("轻量级设备检查完成: 设备检测={DeviceOk}, 授权验证={AuthOk}, 整体健康={Healthy}", 
                    fullResult.IsSuccess, fullResult.IsAuthorized, isHealthy);
            }

            return isHealthy;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "轻量级设备检查过程中发生异常");
            return false;
        }
    }

    /// <summary>
    /// 执行完整健康检查
    /// </summary>
    /// <returns>健康状态</returns>
    private async Task<bool> PerformFullHealthCheckAsync()
    {
        try
        {
            if (_config.EnableDetailedLogging)
            {
                _logger.LogDebug("执行完整健康检查...");
            }

            var result = await _dongleCheckService.CheckDongleAsync();
            bool isHealthy = result.IsSuccess && result.IsAuthorized;
            
            if (_config.EnableDetailedLogging)
            {
                _logger.LogDebug("完整健康检查完成: 设备检测={DeviceOk}, 授权验证={AuthOk}, 硬件测试={HardwareOk}, 整体健康={Healthy}", 
                    result.IsSuccess, 
                    result.IsAuthorized, 
                    result.DongleInfo?.CanOpenClose ?? false,
                    isHealthy);
            }

            return isHealthy;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "完整健康检查过程中发生异常");
            return false;
        }
    }
}
