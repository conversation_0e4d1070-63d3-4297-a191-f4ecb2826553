using System.Collections.ObjectModel;
using System.Diagnostics;
using System.Security.Cryptography;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Web;
using Furion.EventBus;
using Furion.JsonSerialization;
using Microsoft.International.Converters.PinYinConverter;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace IotPlatform.Core.Extension;

/// <summary>
///     字符串<see cref="string" />类型的扩展辅助操作类.
/// </summary>
[SuppressSniffer]
public static class StringExtension
{
    #region 正则表达式

    /// <summary>
    ///     指示所指定的正则表达式在指定的输入字符串中是否找到了匹配项.
    /// </summary>
    /// <param name="value">要搜索匹配项的字符串.</param>
    /// <param name="pattern">要匹配的正则表达式模式.</param>
    /// <param name="isContains">是否包含，否则全匹配.</param>
    /// <returns>如果正则表达式找到匹配项，则为 true；否则，为 false.</returns>
    public static bool IsMatch(this string value, string pattern, bool isContains = true)
    {
        if (value == null)
        {
            return false;
        }

        return isContains
            ? Regex.IsMatch(value, pattern)
            : Regex.Match(value, pattern).Success;
    }

    /// <summary>
    ///     在指定的输入字符串中匹配并替换符合指定正则表达式的子串.
    /// </summary>
    public static string ReplaceRegex(this string value, string pattern, string replacement)
    {
        if (value == null)
        {
            return string.Empty;
        }

        return Regex.Replace(value, pattern, replacement, RegexOptions.IgnoreCase);
    }

    #endregion

    #region 其他操作

    /// <summary>
    ///     判断类型是否实现某个泛型
    /// </summary>
    /// <param name="type">类型</param>
    /// <param name="generic">泛型类型</param>
    /// <returns>bool</returns>
    public static bool HasImplementedRawGeneric(this Type type, Type generic)
    {
        // 检查接口类型
        bool isTheRawGenericType = type.GetInterfaces().Any(IsTheRawGenericType);
        if (isTheRawGenericType)
        {
            return true;
        }

        // 检查类型
        while (type != null && type != typeof(object))
        {
            isTheRawGenericType = IsTheRawGenericType(type);
            if (isTheRawGenericType)
            {
                return true;
            }

            type = type.BaseType;
        }

        return false;

        // 判断逻辑
        bool IsTheRawGenericType(Type type)
        {
            return generic == (type.IsGenericType ? type.GetGenericTypeDefinition() : type);
        }
    }

    /// <summary>
    ///     处理泛型类型转字符串打印问题
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    public static string HandleGenericType(this Type type)
    {
        if (type == null)
        {
            return string.Empty;
        }

        // 处理常见的基础类型，返回友好的名称
        string friendlyName = GetFriendlyTypeName(type);
        if (!string.IsNullOrEmpty(friendlyName))
        {
            return friendlyName;
        }

        string typeName = type.Name;

        // 处理泛型类型问题
        if (type.IsConstructedGenericType)
        {
            string prefix = type.GetGenericArguments()
                .Select(genericArg => HandleGenericType(genericArg))
                .Aggregate((previous, current) => previous + ", " + current);

            typeName = typeName.Split('`').First() + "<" + prefix + ">";
        }

        return typeName;
    }

    /// <summary>
    ///     获取类型的友好名称
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    private static string GetFriendlyTypeName(Type type)
    {
        // 处理基础类型
        if (type == typeof(string)) return "string";
        if (type == typeof(int)) return "int";
        if (type == typeof(long)) return "long";
        if (type == typeof(short)) return "short";
        if (type == typeof(byte)) return "byte";
        if (type == typeof(bool)) return "bool";
        if (type == typeof(float)) return "float";
        if (type == typeof(double)) return "double";
        if (type == typeof(decimal)) return "decimal";
        if (type == typeof(char)) return "char";
        if (type == typeof(object)) return "object";
        if (type == typeof(DateTime)) return "DateTime";
        if (type == typeof(DateTimeOffset)) return "DateTimeOffset";
        if (type == typeof(TimeSpan)) return "TimeSpan";
        if (type == typeof(Guid)) return "Guid";

        // 处理可空类型
        if (type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>))
        {
            Type underlyingType = Nullable.GetUnderlyingType(type);
            return GetFriendlyTypeName(underlyingType) + "?";
        }

        // 处理常见的集合类型
        if (type.IsGenericType)
        {
            Type genericTypeDefinition = type.GetGenericTypeDefinition();
            Type[] genericArguments = type.GetGenericArguments();

            if (genericTypeDefinition == typeof(List<>))
            {
                return $"List<{HandleGenericType(genericArguments[0])}>";
            }
            if (genericTypeDefinition == typeof(IList<>))
            {
                return $"IList<{HandleGenericType(genericArguments[0])}>";
            }
            if (genericTypeDefinition == typeof(ICollection<>))
            {
                return $"ICollection<{HandleGenericType(genericArguments[0])}>";
            }
            if (genericTypeDefinition == typeof(IEnumerable<>))
            {
                return $"IEnumerable<{HandleGenericType(genericArguments[0])}>";
            }
            if (genericTypeDefinition == typeof(Dictionary<,>))
            {
                return $"Dictionary<{HandleGenericType(genericArguments[0])}, {HandleGenericType(genericArguments[1])}>";
            }
            if (genericTypeDefinition == typeof(IDictionary<,>))
            {
                return $"IDictionary<{HandleGenericType(genericArguments[0])}, {HandleGenericType(genericArguments[1])}>";
            }
            if (genericTypeDefinition == typeof(KeyValuePair<,>))
            {
                return $"KeyValuePair<{HandleGenericType(genericArguments[0])}, {HandleGenericType(genericArguments[1])}>";
            }
        }

        // 处理数组类型
        if (type.IsArray)
        {
            Type elementType = type.GetElementType();
            int rank = type.GetArrayRank();
            string brackets = rank == 1 ? "[]" : "[" + new string(',', rank - 1) + "]";
            return HandleGenericType(elementType) + brackets;
        }

        return string.Empty; // 返回空字符串表示没有找到友好名称
    }

    /// <summary>
    ///     转换cron表达式
    /// </summary>
    /// <param name="cron"></param>
    /// <returns></returns>
    public static string ConvertCron(this string cron)
    {
        string[] sp = cron.Split(' ');
        string newCron = "";
        for (int i = 0; i < 6; i++)
        {
            newCron += " " + sp[i];
        }

        return newCron.TrimStart().TrimEnd();
    }

    /// <summary>
    ///     自定义方法，将双精度浮点数格式化为字符串，避免科学计数法
    /// </summary>
    /// <param name="number"></param>
    /// <returns></returns>
    public static string FormatDecimal(this decimal number)
    {
        // 指定小数位数和固定的显示格式
        return number.ToString("0.#############################"); // 适当增加 # 的数量以满足精度需求
    }

    /// <summary>
    ///     中文转拼英
    /// </summary>
    /// <param name="chineseText"></param>
    /// <returns></returns>
    public static string ConvertPinYin(this string chineseText)
    {
        StringBuilder pinyinBuilder = new();
        foreach (char c in chineseText)
        {
            ChineseChar chineseChar = new(c);
            ReadOnlyCollection<string> pinyinArray = chineseChar.Pinyins;
            foreach (string pinyin in pinyinArray)
            {
                if (!string.IsNullOrWhiteSpace(pinyin))
                {
                    string pinyinWithoutNumber = RemoveNumericTone(pinyin);
                    pinyinBuilder.Append(pinyinWithoutNumber);
                    break;
                }
            }
        }

        string pinyinText = pinyinBuilder.ToString();
        return pinyinText.ParseToPascalCase();
    }

    /// <summary>
    /// </summary>
    /// <param name="pinyin"></param>
    /// <returns></returns>
    private static string RemoveNumericTone(string pinyin)
    {
        StringBuilder pinyinWithoutNumber = new();
        foreach (char c in pinyin)
        {
            if (!char.IsNumber(c))
            {
                pinyinWithoutNumber.Append(c);
            }
        }

        return pinyinWithoutNumber.ToString();
    }

    /// <summary>
    ///     字典过滤
    /// </summary>
    /// <param name="a"></param>
    /// <param name="b"></param>
    /// <typeparam name="TKey"></typeparam>
    /// <typeparam name="TValue"></typeparam>
    /// <returns></returns>
    public static bool DictionaryEqual<TKey, TValue>(this IDictionary<TKey, TValue> a, IDictionary<TKey, TValue> b)
    {
        if (a == null && b == null)
        {
            return true;
        }

        if (a == null || b == null || a.Count != b.Count)
        {
            return false;
        }

        foreach (KeyValuePair<TKey, TValue> kvp in a)
        {
            if (!b.TryGetValue(kvp.Key, out TValue value) || !Equals(kvp.Value, value))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// </summary>
    /// <param name="cipherText"></param>
    /// <param name="Key"></param>
    /// <returns></returns>
    public static string DecryptAes(byte[] cipherText, byte[] Key)
    {
        using (Aes aesAlg = Aes.Create())
        {
            aesAlg.Key = Key;
            aesAlg.IV = Key;
            aesAlg.Mode = CipherMode.ECB;
            aesAlg.Padding = PaddingMode.PKCS7;

            ICryptoTransform decryptor = aesAlg.CreateDecryptor(aesAlg.Key, aesAlg.IV);

            using (MemoryStream ms = new(cipherText))
            {
                using (CryptoStream cs = new(ms, decryptor, CryptoStreamMode.Read))
                {
                    using (StreamReader sr = new(cs))
                    {
                        return sr.ReadToEnd();
                    }
                }
            }
        }
    }

    /// <summary>
    ///     获取 JsonElement 实际的值
    /// </summary>
    /// <param name="value">对象值</param>
    /// <returns>
    ///     <see cref="object" />
    /// </returns>
    public static object? GetJsonElementValue(this object? value)
    {
        if (value == null || value is not JsonElement ele)
        {
            return value;
        }

        // 处理 Array 类型的值
        if (ele.ValueKind == JsonValueKind.Array)
        {
            JsonElement.ArrayEnumerator arrEle = ele.EnumerateArray();
            int length = ele.GetArrayLength();
            object[] arr = new object[length];

            int i = 0;
            foreach (JsonElement item in arrEle)
            {
                // 递归处理
                arr[i] = GetJsonElementValue(item);
                i++;
            }

            return arr;
        }

        // 处理单个值
        object? actValue = ele.ValueKind switch
        {
            JsonValueKind.String => ele.GetString(),
            JsonValueKind.True => true,
            JsonValueKind.False => false,
            JsonValueKind.Null => default,
            JsonValueKind.Number => ele.TryGetInt32(out int num) ? num : ele.GetInt64(),
            _ => throw new ArgumentException("Only int, long, string, boolean and null types or array types constructed by them are supported.")
        };

        // 处理 long 类型问题
        if (actValue is long longValue and >= int.MinValue and <= int.MaxValue)
        {
            actValue = (int)longValue;
        }

        return actValue;
    }

    /// <summary>
    ///     Object 转 JSON字符串(时间格式不转换).
    /// </summary>
    /// <param name="obj"></param>
    /// <returns></returns>
    public static string ToJson(this object obj)
    {
        return obj == null ? string.Empty : JsonConvert.SerializeObject(obj);
    }

    /// <summary>
    ///     Object 转 JSON字符串(时间格式不转换).
    /// </summary>
    /// <param name="obj"></param>
    /// <param name="formatting"></param>
    /// <returns></returns>
    public static string ToJson(this object obj, Formatting formatting)
    {
        return obj == null ? string.Empty : JsonConvert.SerializeObject(obj, formatting);
    }

    /// <summary>
    ///     Json字符串反序列化成对象
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="json"></param>
    /// <returns></returns>
    public static T ToObjectOld<T>(this string json)
    {
        return _ = JsonConvert.DeserializeObject<T>(json) ?? default(T);
    }

    /// <summary>
    ///     Json字符串反序列化成对象
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="json"></param>
    /// <returns></returns>
    public static T ToObjectOld<T>(this object json)
    {
        return _ = JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(json)) ?? default(T);
    }

    public static List<Dictionary<string, object>> ListDicToListDic(this List<Dictionary<string, object>>? list)
    {
        if (list == null)
        {
            return new List<Dictionary<string, object>>();
        }

        foreach (Dictionary<string, object> row in list)
        {
            foreach ((string key, object value) in row)
            {
                row[key] = value.GetJsonElementValue();
            }
        }

        return list;
    }

    /// <summary>
    ///     指示指定的字符串是 null 或者 System.String.Empty 字符串.
    /// </summary>
    [DebuggerStepThrough]
    public static bool IsNullOrEmpty(this string value)
    {
        return string.IsNullOrEmpty(value);
    }

    /// <summary>
    ///     指示指定的字符串是 null、空或者仅由空白字符组成.
    /// </summary>
    [DebuggerStepThrough]
    public static bool IsNullOrWhiteSpace(this string value)
    {
        return string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    ///     指示指定的字符串是 null、空或者仅由空白字符组成.
    /// </summary>
    [DebuggerStepThrough]
    public static bool IsMissing(this string value)
    {
        return string.IsNullOrWhiteSpace(value);
    }

    /// <summary>
    ///     为指定格式的字符串填充相应对象来生成字符串.
    /// </summary>
    /// <param name="format">字符串格式，占位符以{n}表示.</param>
    /// <param name="args">用于填充占位符的参数.</param>
    /// <returns>格式化后的字符串.</returns>
    [DebuggerStepThrough]
    public static string FormatWith(this string format, params object[] args)
    {
        return string.Format(CultureInfo.CurrentCulture, format, args);
    }

    /// <summary>
    ///     将字符串反转.
    /// </summary>
    /// <param name="value">要反转的字符串.</param>
    public static string ReverseString(this string value)
    {
        return new string(value.Reverse().ToArray());
    }

    /// <summary>
    ///     单词变成单数形式.
    /// </summary>
    /// <param name="word"></param>
    /// <returns></returns>
    public static string ToSingular(this string word)
    {
        Regex plural1 = new("(?<keep>[^aeiou])ies$");
        Regex plural2 = new("(?<keep>[aeiou]y)s$");
        Regex plural3 = new("(?<keep>[sxzh])es$");
        Regex plural4 = new("(?<keep>[^sxzhyu])s$");

        if (plural1.IsMatch(word))
        {
            return plural1.Replace(word, "${keep}y");
        }

        if (plural2.IsMatch(word))
        {
            return plural2.Replace(word, "${keep}");
        }

        if (plural3.IsMatch(word))
        {
            return plural3.Replace(word, "${keep}");
        }

        if (plural4.IsMatch(word))
        {
            return plural4.Replace(word, "${keep}");
        }

        return word;
    }

    /// <summary>
    ///     单词变成复数形式.
    /// </summary>
    /// <param name="word"></param>
    /// <returns></returns>
    public static string ToPlural(this string word)
    {
        Regex plural1 = new("(?<keep>[^aeiou])y$");
        Regex plural2 = new("(?<keep>[aeiou]y)$");
        Regex plural3 = new("(?<keep>[sxzh])$");
        Regex plural4 = new("(?<keep>[^sxzhy])$");

        if (plural1.IsMatch(word))
        {
            return plural1.Replace(word, "${keep}ies");
        }

        if (plural2.IsMatch(word))
        {
            return plural2.Replace(word, "${keep}s");
        }

        if (plural3.IsMatch(word))
        {
            return plural3.Replace(word, "${keep}es");
        }

        if (plural4.IsMatch(word))
        {
            return plural4.Replace(word, "${keep}s");
        }

        return word;
    }

    /// <summary>
    ///     判断指定路径是否图片文件.
    /// </summary>
    public static bool IsImageFile(this string filename)
    {
        if (!File.Exists(filename))
        {
            return false;
        }

        byte[] fileData = File.ReadAllBytes(filename);
        if (fileData.Length == 0)
        {
            return false;
        }

        ushort code = BitConverter.ToUInt16(fileData, 0);
        switch (code)
        {
            // bmp
            case 0x4D42:
            // jpg
            case 0xD8FF:
            // gif
            case 0x4947:
            // png
            case 0x5089:
                return true;
            default:
                return false;
        }
    }

    /// <summary>
    ///     以指定字符串作为分隔符将指定字符串分隔成数组.
    /// </summary>
    /// <param name="value">要分割的字符串.</param>
    /// <param name="strSplit">字符串类型的分隔符.</param>
    /// <param name="removeEmptyEntries">是否移除数据中元素为空字符串的项.</param>
    /// <returns>分割后的数据.</returns>
    public static string[] Split(this string value, string strSplit, bool removeEmptyEntries = false)
    {
        return value.Split(new[] { strSplit }, removeEmptyEntries ? StringSplitOptions.RemoveEmptyEntries : StringSplitOptions.None);
    }

    /// <summary>
    ///     获取字符串的MD5 Hash值.
    /// </summary>
    public static string ToMd5Hash(this string value)
    {
        return HashHelper.GetMd5(value);
    }

    /// <summary>
    ///     支持汉字的字符串长度，汉字长度计为2.
    /// </summary>
    /// <param name="value">参数字符串.</param>
    /// <returns>当前字符串的长度，汉字长度为2.</returns>
    public static int TextLength(this string value)
    {
        ASCIIEncoding ascii = new();
        int tempLen = 0;
        byte[] bytes = ascii.GetBytes(value);
        foreach (byte b in bytes)
        {
            if (b == 63)
            {
                tempLen += 2;
            }
            else
            {
                tempLen += 1;
            }
        }

        return tempLen;
    }

    /// <summary>
    ///     将JSON字符串还原为对象.
    /// </summary>
    /// <typeparam name="T">要转换的目标类型.</typeparam>
    /// <param name="json">JSON字符串. </param>
    /// <returns></returns>
    public static T? FromJsonString<T>(this string json)
    {
        return JsonSerializer.Deserialize<T>(json);
    }

    /// <summary>
    ///     将JSON字符串还原为对象.
    /// </summary>
    /// <param name="json">JSON字符串. </param>
    /// <param name="type">数据类型.</param>
    public static object? FromJsonString(this string json, Type type)
    {
        return JsonSerializer.Deserialize(json, type);
    }

    /// <summary>
    ///     给URL添加查询参数.
    /// </summary>
    /// <param name="url">URL字符串.</param>
    /// <param name="queries">要添加的参数，形如："id=1,cid=2".</param>
    /// <returns></returns>
    public static string AddUrlQuery(this string url, params string[] queries)
    {
        foreach (string query in queries)
        {
            if (!url.Contains('?'))
            {
                url += "?";
            }
            else if (!url.EndsWith("&"))
            {
                url += "&";
            }

            url = url + query;
        }

        return url;
    }

    /// <summary>
    ///     获取URL中指定参数的值，不存在返回空字符串.
    /// </summary>
    public static string GetUrlQuery(this string url, string key)
    {
        Uri uri = new(url);
        string query = uri.Query;
        if (query.IsNullOrEmpty())
        {
            return string.Empty;
        }

        query = query.TrimStart('?');
        Dictionary<string, string>? dict = (from m in query.Split("&", true)
                                            let strs = m.Split("=")
                                            select new KeyValuePair<string, string>(strs[0], strs[1]))
            .ToDictionary(m => m.Key, m => m.Value);
        if (dict.ContainsKey(key))
        {
            return dict[key];
        }

        return string.Empty;
    }

    /// <summary>
    ///     给URL添加 # 参数.
    /// </summary>
    /// <param name="url">URL字符串.</param>
    /// <param name="query">要添加的参数.</param>
    /// <returns></returns>
    public static string AddHashFragment(this string url, string query)
    {
        if (!url.Contains("#"))
        {
            url += "#";
        }

        return url + query;
    }

    /// <summary>
    ///     将字符串转换为<see cref="byte" />[]数组，默认编码为<see cref="Encoding.UTF8" />.
    /// </summary>
    public static byte[] ToBytes(this string value, Encoding? encoding = null)
    {
        if (encoding == null)
        {
            encoding = Encoding.UTF8;
        }

        return encoding.GetBytes(value);
    }

    /// <summary>
    ///     将<see cref="byte" />[]数组转换为字符串，默认编码为<see cref="Encoding.UTF8" />.
    /// </summary>
    public static string ToString2(this byte[] bytes, Encoding? encoding = null)
    {
        if (encoding == null)
        {
            encoding = Encoding.UTF8;
        }

        return encoding.GetString(bytes);
    }

    /// <summary>
    ///     将<see cref="byte" />[]数组转换为Base64字符串.
    /// </summary>
    public static string ToBase64String(this byte[] bytes)
    {
        return Convert.ToBase64String(bytes);
    }

    /// <summary>
    ///     将字符串转换为Base64字符串，默认编码为<see cref="Encoding.UTF8" />.
    /// </summary>
    /// <param name="source">正常的字符串.</param>
    /// <param name="encoding">编码.</param>
    /// <returns>Base64字符串.</returns>
    public static string ToBase64String(this string source, Encoding? encoding = null)
    {
        if (encoding == null)
        {
            encoding = Encoding.UTF8;
        }

        return Convert.ToBase64String(encoding.GetBytes(source));
    }

    /// <summary>
    ///     将Base64字符串转换为正常字符串，默认编码为<see cref="Encoding.UTF8" />.
    /// </summary>
    /// <param name="base64String">Base64字符串.</param>
    /// <param name="encoding">编码.</param>
    /// <returns>正常字符串.</returns>
    public static string FromBase64String(this string base64String, Encoding? encoding = null)
    {
        if (encoding == null)
        {
            encoding = Encoding.UTF8;
        }

        byte[] bytes = Convert.FromBase64String(base64String);
        return encoding.GetString(bytes);
    }

    /// <summary>
    ///     将字符串进行UrlDecode解码.
    /// </summary>
    /// <param name="source">待UrlDecode解码的字符串.</param>
    /// <returns>UrlDecode解码后的字符串.</returns>
    public static string ToUrlDecode(this string source)
    {
        return HttpUtility.UrlDecode(source);
    }

    /// <summary>
    ///     将字符串进行UrlEncode编码.
    /// </summary>
    /// <param name="source">待UrlEncode编码的字符串.</param>
    /// <returns>UrlEncode编码后的字符串.</returns>
    public static string ToUrlEncode(this string source)
    {
        return HttpUtility.UrlEncode(source);
    }

    /// <summary>
    ///     将字符串进行HtmlDecode解码.
    /// </summary>
    /// <param name="source">待HtmlDecode解码的字符串.</param>
    /// <returns>HtmlDecode解码后的字符串.</returns>
    public static string ToHtmlDecode(this string source)
    {
        return HttpUtility.HtmlDecode(source);
    }

    /// <summary>
    ///     将字符串进行HtmlEncode编码.
    /// </summary>
    /// <param name="source">待HtmlEncode编码的字符串.</param>
    /// <returns>HtmlEncode编码后的字符串.</returns>
    public static string ToHtmlEncode(this string source)
    {
        return HttpUtility.HtmlEncode(source);
    }

    /// <summary>
    ///     将字符串转换为十六进制字符串，默认编码为<see cref="Encoding.UTF8" />.
    /// </summary>
    public static string ToHexString(this string source, Encoding? encoding = null)
    {
        if (encoding == null)
        {
            encoding = Encoding.UTF8;
        }

        byte[] bytes = encoding.GetBytes(source);
        return bytes.ToHexString();
    }

    /// <summary>
    ///     将十六进制字符串转换为常规字符串，默认编码为<see cref="Encoding.UTF8" />.
    /// </summary>
    public static string FromHexString(this string hexString, Encoding? encoding = null)
    {
        if (encoding == null)
        {
            encoding = Encoding.UTF8;
        }

        byte[] bytes = hexString.ToHexBytes();
        return encoding.GetString(bytes);
    }

    /// <summary>
    ///     将byte[]编码为十六进制字符串.
    /// </summary>
    /// <param name="bytes">byte[]数组.</param>
    /// <returns>十六进制字符串.</returns>
    public static string ToHexString(this byte[] bytes)
    {
        return bytes.Aggregate(string.Empty, (current, t) => current + t.ToString("X2"));
    }

    /// <summary>
    ///     将十六进制字符串转换为byte[].
    /// </summary>
    /// <param name="hexString">十六进制字符串.</param>
    /// <returns>byte[]数组.</returns>
    public static byte[] ToHexBytes(this string hexString)
    {
        hexString = hexString ?? string.Empty;
        hexString = hexString.Replace(" ", string.Empty);
        byte[] bytes = new byte[hexString.Length / 2];
        for (int i = 0; i < bytes.Length; i++)
        {
            bytes[i] = Convert.ToByte(hexString.Substring(i * 2, 2), 16);
        }

        return bytes;
    }

    /// <summary>
    ///     将字符串进行Unicode编码，变成形如“\u7f16\u7801”的形式.
    /// </summary>
    /// <param name="source">要进行编号的字符串.</param>
    public static string ToUnicodeString(this string source)
    {
        Regex regex = new(@"[^\u0000-\u00ff]");
        return regex.Replace(source, m => string.Format(@"\u{0:x4}", (short)m.Value[0]));
    }

    /// <summary>
    ///     将形如“\u7f16\u7801”的Unicode字符串解码.
    /// </summary>
    public static string FromUnicodeString(this string source)
    {
        Regex regex = new(@"\\u([0-9a-fA-F]{4})", RegexOptions.Compiled);
        return regex.Replace(
            source,
            m =>
            {
                short s;
                if (short.TryParse(m.Groups[1].Value, NumberStyles.HexNumber, CultureInfo.InstalledUICulture, out s))
                {
                    return string.Empty + (char)s;
                }

                return m.Value;
            });
    }

    /// <summary>
    ///     将驼峰字符串按单词拆分并转换成小写，再以特定字符串分隔.
    /// </summary>
    /// <param name="str">待转换的字符串.</param>
    /// <param name="splitStr">分隔符字符.</param>
    /// <returns></returns>
    public static string UpperToLowerAndSplit(this string str, string splitStr = "-")
    {
        if (string.IsNullOrEmpty(str))
        {
            return str;
        }

        List<string> words = new();
        while (str.Length > 0)
        {
            char c = str.FirstOrDefault(char.IsUpper);
            if (c == default(char))
            {
                words.Add(str);
                break;
            }

            int upperIndex = str.IndexOf(c);

            // admin
            if (upperIndex < 0)
            {
                return str;
            }

            // adminAdmin
            if (upperIndex > 0)
            {
                string first = str.Substring(0, upperIndex);
                words.Add(first);
                str = str.Substring(upperIndex, str.Length - upperIndex);
                continue;
            }

            str = char.ToLower(str[0]) + str.Substring(1, str.Length - 1);
        }

        return words.ExpandAndToString(splitStr);
    }

    /// <summary>
    ///     将驼峰字符串的第一个字符小写.
    /// </summary>
    public static string ToLowerCase(this string str)
    {
        if (string.IsNullOrEmpty(str) || !char.IsUpper(str[0]))
        {
            return str;
        }

        if (str.Length == 1)
        {
            return char.ToLower(str[0]).ToString();
        }

        return char.ToLower(str[0]) + str.Substring(1, str.Length - 1);
    }

    /// <summary>
    ///     将小驼峰字符串的第一个字符大写.
    /// </summary>
    public static string ToUpperCase(this string str)
    {
        if (string.IsNullOrEmpty(str) || !char.IsLower(str[0]))
        {
            return str;
        }

        if (str.Length == 1)
        {
            return char.ToUpper(str[0]).ToString();
        }

        return char.ToUpper(str[0]) + str.Substring(1, str.Length - 1);
    }

    /// <summary>
    ///     计算当前字符串与指定字符串的编辑距离(相似度).
    /// </summary>
    /// <param name="source">源字符串.</param>
    /// <param name="target">目标字符串.</param>
    /// <param name="similarity">输出相似度.</param>
    /// <param name="ignoreCase">是否忽略大小写.</param>
    /// <returns>编辑距离.</returns>
    public static int LevenshteinDistance(this string source, string target, out double similarity, bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(source))
        {
            if (string.IsNullOrEmpty(target))
            {
                similarity = 1;
                return 0;
            }

            similarity = 0;
            return target.Length;
        }

        if (string.IsNullOrEmpty(target))
        {
            similarity = 0;
            return source.Length;
        }

        string from, to;
        if (ignoreCase)
        {
            from = source;
            to = target;
        }
        else
        {
            from = source.ToLower();
            to = source.ToLower();
        }

        int m = from.Length, n = to.Length;
        int[,] mn = new int[m + 1, n + 1];
        for (int i = 0; i <= m; i++)
        {
            mn[i, 0] = i;
        }

        for (int j = 1; j <= n; j++)
        {
            mn[0, j] = j;
        }

        for (int i = 1; i <= m; i++)
        {
            char c = from[i - 1];
            for (int j = 1; j <= n; j++)
            {
                if (c == to[j - 1])
                {
                    mn[i, j] = mn[i - 1, j - 1];
                }
                else
                {
                    mn[i, j] = Math.Min(mn[i - 1, j - 1], Math.Min(mn[i - 1, j], mn[i, j - 1])) + 1;
                }
            }
        }

        int maxLength = Math.Max(m, n);
        similarity = (double)(maxLength - mn[m, n]) / maxLength;
        return mn[m, n];
    }

    /// <summary>
    ///     计算两个字符串的相似度，应用公式：相似度=kq*q/(kq*q+kr*r+ks*s)(kq>0,kr>=0,ka>=0)
    ///     其中，q是字符串1和字符串2中都存在的单词的总数，s是字符串1中存在，字符串2中不存在的单词总数，r是字符串2中存在，字符串1中不存在的单词总数. kq,kr和ka分别是q,r,s的权重，根据实际的计算情况，我们设kq=2，kr=ks=1.
    /// </summary>
    /// <param name="source">源字符串.</param>
    /// <param name="target">目标字符串.</param>
    /// <param name="ignoreCase">是否忽略大小写.</param>
    /// <returns>字符串相似度.</returns>
    public static double GetSimilarityWith(this string source, string target, bool ignoreCase = false)
    {
        if (string.IsNullOrEmpty(source) && string.IsNullOrEmpty(target))
        {
            return 1;
        }

        if (string.IsNullOrEmpty(source) || string.IsNullOrEmpty(target))
        {
            return 0;
        }

        const double kq = 2, kr = 1, ks = 1;
        char[] sourceChars = source.ToCharArray(), targetChars = target.ToCharArray();

        // 获取交集数量
        int q = sourceChars.Intersect(targetChars).Count(), s = sourceChars.Length - q, r = targetChars.Length - q;
        return kq * q / ((kq * q) + (kr * r) + (ks * s));
    }

    #endregion

    /// <summary>
    /// 版本号
    /// </summary>
    public static string Version { get; set; } = "1.0.0";
}