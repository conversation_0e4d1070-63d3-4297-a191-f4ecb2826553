namespace IotPlatform.Core.Attribute;

/// <summary>
///     OpenAPI 参数文档特性
/// </summary>
[AttributeUsage(AttributeTargets.Parameter)]
public class OpenApiParameterAttribute : System.Attribute
{
    /// <summary>
    ///     参数描述
    /// </summary>
    public string Description { get; set; }

    /// <summary>
    ///     参数类型显示名称
    /// </summary>
    public string TypeName { get; set; }

    /// <summary>
    ///     是否必需
    /// </summary>
    public bool IsRequired { get; set; }

    /// <summary>
    ///     参数示例
    /// </summary>
    public string Example { get; set; }

    /// <summary>
    ///     额外的参数说明列表（用于复杂类型的详细说明）
    /// </summary>
    public string[] ExtraDescriptions { get; set; }

    /// <summary>
    ///     构造函数
    /// </summary>
    /// <param name="description">参数描述</param>
    public OpenApiParameterAttribute(string description)
    {
        Description = description;
    }
}
