{
  "$schema": "https://gitee.com/dotnetchina/Furion/raw/v4/schemas/v4/furion-schema.json",
  "DongleLicense": {
    // 检查间隔（分钟）
    "CheckIntervalMinutes": 1,
    // 是否启用检查
    "EnableCheck": true,
    // 是否测试打开关闭操作
    "TestOpenClose": true,
    // 打开关闭操作超时时间（毫秒）
    "OpenCloseTimeoutMs": 5000,
    // 是否记录详细日志
    "EnableDetailedLogging": true,
    // 检查失败时是否发送告警
    "SendAlarmOnFailure": true,
    // 健康检查缓存时间（秒）- 用于GetHealth接口性能优化
    "HealthCheckCacheSeconds": 30,
    // 是否启用快速健康检查模式（跳过硬件操作测试）
    "EnableFastHealthCheck": true,
    // 快速检查时的最大缓存时间（秒）
    "FastCheckMaxCacheSeconds": 60,
    // 健康检查失败时的重试间隔（秒）
    "HealthCheckRetryIntervalSeconds": 5
  }
}
